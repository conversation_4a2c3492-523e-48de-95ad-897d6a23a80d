part of 'ball_rating_bloc.dart';

abstract class BallRatingEvent extends Equatable {
  const BallRatingEvent();

  @override
  List<Object> get props => [];
}

class GetBallRankingEvent extends BallRatingEvent {
  const GetBallRankingEvent();
}

class GetBallHistoryEvent extends BallRatingEvent {
  const GetBallHistoryEvent();
}

class GetBallEarningMatchesEvent extends BallRatingEvent {
  const GetBallEarningMatchesEvent();
}

class GetBallPromotionsEvent extends BallRatingEvent {
  const GetBallPromotionsEvent();
}

class GetBallPackagesEvent extends BallRatingEvent {
  const GetBallPackagesEvent();
}

class GetCurrentUserBallInfoEvent extends BallRatingEvent {
  const GetCurrentUserBallInfoEvent();
}

class SelectBallPackageEvent extends BallRatingEvent {
  final int packageId;
  
  const SelectBallPackageEvent({required this.packageId});
  
  @override
  List<Object> get props => [packageId];
}

class PurchaseBallPackageEvent extends BallRatingEvent {
  final int packageId;
  final VoidCallback? onSuccess;
  final Function(String)? onError;
  
  const PurchaseBallPackageEvent({
    required this.packageId,
    this.onSuccess,
    this.onError,
  });
  
  @override
  List<Object> get props => [packageId];
}

class ChangeTabEvent extends BallRatingEvent {
  final int tabIndex;
  
  const ChangeTabEvent({required this.tabIndex});
  
  @override
  List<Object> get props => [tabIndex];
}

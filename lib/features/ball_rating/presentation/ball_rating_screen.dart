import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/ball_rating/presentation/bloc/ball_rating_bloc.dart';
import 'package:echipta/features/ball_rating/presentation/widgets/w_ball_tab_switcher.dart';
import 'package:echipta/features/ball_rating/presentation/views/ball_rating_view.dart';
import 'package:echipta/features/ball_rating/presentation/views/ball_history_view.dart';
import 'package:echipta/features/ball_rating/presentation/views/ball_earning_view.dart';
import 'package:echipta/features/ball_rating/presentation/views/ball_promotions_view.dart';
import 'package:echipta/features/ball_rating/presentation/views/ball_purchase_view.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:gap/gap.dart';

class BallRatingScreen extends StatefulWidget {
  const BallRatingScreen({super.key});

  @override
  State<BallRatingScreen> createState() => _BallRatingScreenState();
}

class _BallRatingScreenState extends State<BallRatingScreen> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    
    // Load initial data
    context.read<BallRatingBloc>().add(const GetCurrentUserBallInfoEvent());
    // Load data for the first tab (Ball History) since Rating uses RankBloc
    context.read<BallRatingBloc>().add(const GetBallHistoryEvent());
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onTabChanged(int index) {
    context.read<BallRatingBloc>().add(ChangeTabEvent(tabIndex: index));
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
    
    // Load data for the selected tab
    switch (index) {
      case 0:
        // Rating data is already loaded from RankBloc
        break;
      case 1:
        context.read<BallRatingBloc>().add(const GetBallHistoryEvent());
        break;
      case 2:
        context.read<BallRatingBloc>().add(const GetBallEarningMatchesEvent());
        break;
      case 3:
        context.read<BallRatingBloc>().add(const GetBallPromotionsEvent());
        break;
    }
  }

  void _showBallPurchaseView() {
    context.read<BallRatingBloc>().add(const GetBallPackagesEvent());
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BlocProvider.value(
        value: context.read<BallRatingBloc>(),
        child: const BallPurchaseView(),
      ),
    );
  }

  void _showBallUsageView() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.5,
        decoration: const BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.mediumGrey,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Text(
                    LocaleKeys.ballUsage.tr(),
                    style: context.textTheme.displaySmall!.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.black,
                    ),
                  ),
                  const Spacer(),
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: AppColors.fillColor,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Icon(
                        Icons.close,
                        size: 20,
                        color: AppColors.black,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: Center(
                child: Text(
                  'Ball ishlatish funksiyasi tez orada qo\'shiladi',
                  style: context.textTheme.bodyMedium!.copyWith(
                    color: AppColors.darkGrey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: CustomScrollView(
        slivers: [
          // Header with user info
          SliverAppBar(
            backgroundColor: AppColors.primary,
            expandedHeight: 280,
            pinned: true,
            title: Text(
              LocaleKeys.fanRanking.tr(),
              style: context.textTheme.displaySmall!.copyWith(color: AppColors.white),
            ),
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: const BoxDecoration(
                  color: AppColors.primary,
                ),
                child: BlocBuilder<ProfileBloc, ProfileState>(
                  builder: (context, profileState) {
                    return BlocBuilder<BallRatingBloc, BallRatingState>(
                      builder: (context, ballState) {
                        final user = ballState.currentUser.id != 0 
                            ? ballState.currentUser 
                            : null;
                        
                        return Padding(
                          padding: const EdgeInsets.fromLTRB(20, 100, 20, 20),
                          child: Column(
                            children: [
                              // User avatar and info
                              Row(
                                children: [
                                  Container(
                                    width: 80,
                                    height: 80,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      border: Border.all(color: AppColors.white, width: 3),
                                    ),
                                    child: ClipOval(
                                      child: CachedNetworkImage(
                                        imageUrl: user?.avatar ?? profileState.me.picture,
                                        fit: BoxFit.cover,
                                        placeholder: (context, url) => Container(
                                          color: AppColors.mediumGrey,
                                          child: const Icon(Icons.person, color: AppColors.white),
                                        ),
                                        errorWidget: (context, url, error) => Container(
                                          color: AppColors.mediumGrey,
                                          child: const Icon(Icons.person, color: AppColors.white),
                                        ),
                                      ),
                                    ),
                                  ),
                                  const Gap(16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          user?.full_name ?? profileState.me.full_name,
                                          style: context.textTheme.displaySmall!.copyWith(
                                            color: AppColors.white,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                        const Gap(8),
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                          children: [
                                            _buildStatColumn(
                                              '${user?.total_months ?? 1}',
                                              "O'rnim",
                                            ),
                                            _buildStatColumn(
                                              '${user?.total_balls ?? 8}',
                                              'Ball',
                                            ),
                                            _buildStatColumn(
                                              '${user?.total_games ?? 3}',
                                              "O'yinlar",
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              const Gap(24),
                              // Action buttons
                              Row(
                                children: [
                                  Expanded(
                                    child: _buildActionButton(
                                      LocaleKeys.ballPurchase.tr(),
                                      const Color(0xFF1E3A8A), // Dark blue color
                                      AppColors.white,
                                      _showBallPurchaseView,
                                    ),
                                  ),
                                  const Gap(12),
                                  Expanded(
                                    child: _buildActionButton(
                                      LocaleKeys.ballUsage.tr(),
                                      const Color(0xFF1E3A8A), // Same dark blue color
                                      AppColors.white,
                                      _showBallUsageView,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ),
          ),
          
          // Tab switcher
          SliverToBoxAdapter(
            child: BlocBuilder<BallRatingBloc, BallRatingState>(
              builder: (context, state) {
                return WBallTabSwitcher(
                  currentIndex: state.currentTabIndex,
                  onTabChanged: _onTabChanged,
                );
              },
            ),
          ),
          
          // Content based on selected tab
          SliverFillRemaining(
            child: BlocBuilder<BallRatingBloc, BallRatingState>(
              builder: (context, state) {
                return PageView(
                  controller: _pageController,
                  onPageChanged: (index) {
                    context.read<BallRatingBloc>().add(ChangeTabEvent(tabIndex: index));
                  },
                  children: const [
                    BallRatingView(),
                    BallHistoryView(),
                    BallEarningView(),
                    BallPromotionsView(),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatColumn(String value, String label) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          value,
          style: context.textTheme.displayMedium!.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.w700,
            fontSize: 24,
          ),
          textAlign: TextAlign.center,
        ),
        const Gap(4),
        Text(
          label,
          style: context.textTheme.bodySmall!.copyWith(
            color: AppColors.white,
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildActionButton(
    String text,
    Color backgroundColor,
    Color textColor,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 48,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(24),
        ),
        child: Center(
          child: Text(
            text,
            style: context.textTheme.bodyMedium!.copyWith(
              color: textColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:intl/intl.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/ball_rating/presentation/bloc/ball_rating_bloc.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_history_entity.dart';
import 'package:echipta/features/common/widgets/w_empty.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:gap/gap.dart';

class BallHistoryView extends StatelessWidget {
  const BallHistoryView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BallRatingBloc, BallRatingState>(
      builder: (context, state) {
        if (state.historyStatus.isInProgress) {
          return const Center(child: CircularProgressIndicator.adaptive());
        } else if (state.historyStatus.isFailure) {
          return const Center(child: Text("Xatolik yuz berdi!"));
        } else if (state.historyStatus.isSuccess && state.history.isEmpty) {
          return WEmptyScreen();
        }

        return ListView.builder(
          padding: const EdgeInsets.all(20),
          itemCount: state.history.length,
          itemBuilder: (context, index) {
            final historyItem = state.history[index];
            return _buildHistoryItem(context, historyItem);
          },
        );
      },
    );
  }

  Widget _buildHistoryItem(BuildContext context, BallHistoryEntity item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.mediumGrey.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Success/Spent icon
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: item.ball_amount >= 0 ? AppColors.green : AppColors.red,
              shape: BoxShape.circle,
            ),
            child: Icon(
              item.ball_amount >= 0 ? Icons.check : Icons.remove,
              color: AppColors.white,
              size: 20,
            ),
          ),
          const Gap(16),
          
          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.title,
                  style: context.textTheme.bodySmall!.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.black,
                  ),
                ),
                const Gap(4),
                Text(
                  item.description,
                  style: context.textTheme.bodySmall!.copyWith(
                    color: item.ball_amount >= 0 ? AppColors.green : AppColors.red,
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
          
          // Ball amount and date
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${item.ball_amount >= 0 ? '+' : ''}${item.ball_amount} ${LocaleKeys.ball.tr()}',
                style: context.textTheme.bodySmall!.copyWith(
                  fontWeight: FontWeight.w600,
                  color: item.ball_amount >= 0 ? AppColors.green : AppColors.red,
                ),
              ),
              const Gap(4),
              Text(
                DateFormat('dd.MM.yyyy').format(item.created_at),
                style: context.textTheme.bodySmall!.copyWith(
                  color: AppColors.darkGrey,
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

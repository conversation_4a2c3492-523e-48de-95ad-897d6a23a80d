import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:formz/formz.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/ball_rating/presentation/bloc/ball_rating_bloc.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_user_entity.dart';
import 'package:echipta/features/common/widgets/w_empty.dart';
import 'package:echipta/features/rating/presentation/bloc/rank_bloc.dart';
import 'package:echipta/features/profile/presentation/bloc/profile_bloc.dart';
import 'package:gap/gap.dart';

class BallRatingView extends StatelessWidget {
  const BallRatingView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RankBloc, RankState>(
      builder: (context, rankState) {
        if (rankState.ranksStatus.isInProgress) {
          return const Center(child: CircularProgressIndicator.adaptive());
        } else if (rankState.ranksStatus.isFailure) {
          return const Center(child: Text("Xatolik yuz berdi!"));
        } else if (rankState.ranksStatus.isSuccess && rankState.ranks.isEmpty) {
          return WEmptyScreen();
        }

        return BlocBuilder<ProfileBloc, ProfileState>(
          builder: (context, profileState) {
            return Column(
              children: [
                // Header row
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  decoration: const BoxDecoration(
                    color: AppColors.fillColor,
                    border: Border(
                      bottom: BorderSide(color: AppColors.mediumGrey, width: 1),
                    ),
                  ),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 40,
                        child: Text(
                          "O'rni",
                          style: context.textTheme.bodySmall!.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.black,
                          ),
                        ),
                      ),
                      const Gap(16),
                      Expanded(
                        child: Text(
                          "Muxlislar",
                          style: context.textTheme.bodySmall!.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.black,
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 50,
                        child: Text(
                          "O'yin",
                          textAlign: TextAlign.center,
                          style: context.textTheme.bodySmall!.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.black,
                          ),
                        ),
                      ),
                      const Gap(16),
                      SizedBox(
                        width: 50,
                        child: Text(
                          "Ball",
                          textAlign: TextAlign.center,
                          style: context.textTheme.bodySmall!.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.black,
                          ),
                        ),
                      ),
                      const Gap(16),
                      const SizedBox(width: 30), // For +/- indicator
                    ],
                  ),
                ),

                // Ranking list using actual rank data
                Expanded(
                  child: ListView.builder(
                    padding: EdgeInsets.zero,
                    itemCount: rankState.ranks.length,
                    itemBuilder: (context, index) {
                      final rank = rankState.ranks[index];
                      return _buildRankingItem(context, rank, profileState, index + 1);
                    },
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildRankingItem(BuildContext context, dynamic rank, ProfileState profileState, int position) {
    final isCurrentUser = rank.client_id == profileState.me.id;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: isCurrentUser ? AppColors.green2.withValues(alpha: 0.3) : Colors.transparent,
        border: const Border(
          bottom: BorderSide(color: AppColors.mediumGrey, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // Position
          SizedBox(
            width: 40,
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: position <= 3 ? AppColors.green : AppColors.mediumGrey,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  '$position',
                  style: context.textTheme.bodySmall!.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          ),
          const Gap(16),

          // User name
          Expanded(
            child: Text(
              rank.full_name,
              style: context.textTheme.bodySmall!.copyWith(
                fontWeight: FontWeight.w500,
                color: AppColors.black,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Games count
          SizedBox(
            width: 50,
            child: Text(
              '${rank.order_count}',
              textAlign: TextAlign.center,
              style: context.textTheme.bodySmall!.copyWith(
                fontWeight: FontWeight.w500,
                color: AppColors.black,
              ),
            ),
          ),
          const Gap(16),

          // Score
          SizedBox(
            width: 50,
            child: Text(
              '${rank.score}',
              textAlign: TextAlign.center,
              style: context.textTheme.bodySmall!.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.black,
              ),
            ),
          ),
          const Gap(16),

          // Change indicator (always +3 for now)
          SizedBox(
            width: 30,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: AppColors.green,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                '+3',
                textAlign: TextAlign.center,
                style: context.textTheme.bodySmall!.copyWith(
                  color: AppColors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 10,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

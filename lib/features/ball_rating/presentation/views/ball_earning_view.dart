import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:intl/intl.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/ball_rating/presentation/bloc/ball_rating_bloc.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_earning_match_entity.dart';
import 'package:echipta/features/common/widgets/w_empty.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:gap/gap.dart';

class BallEarningView extends StatelessWidget {
  const BallEarningView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BallRatingBloc, BallRatingState>(
      builder: (context, state) {
        if (state.earningMatchesStatus.isInProgress) {
          return const Center(child: CircularProgressIndicator.adaptive());
        } else if (state.earningMatchesStatus.isFailure) {
          return const Center(child: Text("Xatolik yuz berdi!"));
        } else if (state.earningMatchesStatus.isSuccess && state.earningMatches.isEmpty) {
          return WEmptyScreen();
        }

        return Column(
          children: [
            // Description text
            Padding(
              padding: const EdgeInsets.all(20),
              child: Text(
                LocaleKeys.ballEarningDesc.tr(),
                style: context.textTheme.bodySmall!.copyWith(
                  color: AppColors.darkGrey,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            
            // Matches grid
            Expanded(
              child: GridView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.85,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                itemCount: state.earningMatches.length,
                itemBuilder: (context, index) {
                  final match = state.earningMatches[index];
                  return _buildMatchCard(context, match);
                },
              ),
            ),
            
            // Bottom button
            Padding(
              padding: EdgeInsets.fromLTRB(20, 0, 20, context.padding.bottom + 20),
              child: WButton(
                onTap: () {
                  // TODO: Navigate to ticket selection
                },
                txt: LocaleKeys.selectTicket.tr(),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildMatchCard(BuildContext context, BallEarningMatchEntity match) {
    Color cardColor;
    switch (match.card_color) {
      case 'brown':
        cardColor = const Color(0xFF8B4513);
        break;
      case 'pink':
        cardColor = const Color(0xFFE91E63);
        break;
      case 'blue':
        cardColor = const Color(0xFF2196F3);
        break;
      case 'yellow':
        cardColor = AppColors.yellow;
        break;
      default:
        cardColor = const Color(0xFF8B4513);
    }

    return Container(
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Date and time
            Text(
              DateFormat('dd.MM.yyyy | HH:mm').format(match.match_date),
              style: context.textTheme.bodySmall!.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w500,
                fontSize: 11,
              ),
            ),
            const Gap(12),
            
            // Teams
            Expanded(
              child: Row(
                children: [
                  // Team 1
                  Expanded(
                    child: Column(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: CachedNetworkImage(
                              imageUrl: match.team1_logo,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: AppColors.mediumGrey,
                                child: const Icon(Icons.sports_soccer, size: 20),
                              ),
                              errorWidget: (context, url, error) => Container(
                                color: AppColors.mediumGrey,
                                child: const Icon(Icons.sports_soccer, size: 20),
                              ),
                            ),
                          ),
                        ),
                        const Gap(8),
                        Text(
                          match.team1_name,
                          style: context.textTheme.bodySmall!.copyWith(
                            color: AppColors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 10,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  
                  // VS
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: Text(
                      'VS',
                      style: context.textTheme.bodySmall!.copyWith(
                        color: AppColors.white,
                        fontWeight: FontWeight.w700,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  
                  // Team 2
                  Expanded(
                    child: Column(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: CachedNetworkImage(
                              imageUrl: match.team2_logo,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: AppColors.mediumGrey,
                                child: const Icon(Icons.sports_soccer, size: 20),
                              ),
                              errorWidget: (context, url, error) => Container(
                                color: AppColors.mediumGrey,
                                child: const Icon(Icons.sports_soccer, size: 20),
                              ),
                            ),
                          ),
                        ),
                        const Gap(8),
                        Text(
                          match.team2_name,
                          style: context.textTheme.bodySmall!.copyWith(
                            color: AppColors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 10,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:formz/formz.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/ball_rating/presentation/bloc/ball_rating_bloc.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_package_entity.dart';
import 'package:echipta/features/common/widgets/w_button.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:gap/gap.dart';

class BallPurchaseView extends StatelessWidget {
  const BallPurchaseView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.mediumGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Text(
                  LocaleKeys.ballPurchase.tr(),
                  style: context.textTheme.displaySmall!.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.black,
                  ),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppColors.fillColor,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Icon(
                      Icons.close,
                      size: 20,
                      color: AppColors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Description
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              LocaleKeys.ballPurchaseDesc.tr(),
              style: context.textTheme.bodySmall!.copyWith(
                color: AppColors.darkGrey,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
          const Gap(24),
          
          // Package grid
          Expanded(
            child: BlocBuilder<BallRatingBloc, BallRatingState>(
              builder: (context, state) {
                if (state.packagesStatus.isInProgress) {
                  return const Center(child: CircularProgressIndicator.adaptive());
                } else if (state.packagesStatus.isFailure) {
                  return const Center(child: Text("Xatolik yuz berdi!"));
                } else if (state.packagesStatus.isSuccess && state.packages.isEmpty) {
                  return const Center(child: Text("Paketlar mavjud emas"));
                }

                return GridView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 1.2,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                  ),
                  itemCount: state.packages.length,
                  itemBuilder: (context, index) {
                    final package = state.packages[index];
                    return _buildPackageCard(context, package);
                  },
                );
              },
            ),
          ),
          
          // Purchase button
          Padding(
            padding: EdgeInsets.fromLTRB(20, 0, 20, context.padding.bottom + 20),
            child: BlocBuilder<BallRatingBloc, BallRatingState>(
              builder: (context, state) {
                final hasSelection = state.selectedPackageId != null;
                return WButton(
                  onTap: hasSelection ? () {
                    context.read<BallRatingBloc>().add(
                      PurchaseBallPackageEvent(
                        packageId: state.selectedPackageId!,
                        onSuccess: () {
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Ball to\'plami muvaffaqiyatli sotib olindi!'),
                              backgroundColor: AppColors.green,
                              duration: const Duration(seconds: 3),
                            ),
                          );
                        },
                        onError: (error) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Xatolik: $error'),
                              backgroundColor: AppColors.red,
                              duration: const Duration(seconds: 3),
                            ),
                          );
                        },
                      ),
                    );
                  } : () {},
                  txt: LocaleKeys.orderProduct.tr(),
                  btnColor: hasSelection ? AppColors.primary : AppColors.mediumGrey,
                  txtColor: hasSelection ? AppColors.white : AppColors.darkGrey,
                  isLoading: state.purchaseStatus.isInProgress,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPackageCard(BuildContext context, BallPackageEntity package) {
    return GestureDetector(
      onTap: () {
        context.read<BallRatingBloc>().add(
          SelectBallPackageEvent(packageId: package.id),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: package.is_selected ? AppColors.primary.withValues(alpha: 0.1) : AppColors.fillColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: package.is_selected ? AppColors.primary : AppColors.mediumGrey.withValues(alpha: 0.3),
            width: package.is_selected ? 2 : 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Ball icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: package.is_selected ? AppColors.primary : AppColors.mediumGrey,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.sports_soccer,
                  color: AppColors.white,
                  size: 20,
                ),
              ),
              const Gap(12),
              
              // Ball amount
              Text(
                '${package.ball_amount}',
                style: context.textTheme.displayMedium!.copyWith(
                  fontWeight: FontWeight.w700,
                  color: package.is_selected ? AppColors.primary : AppColors.black,
                  fontSize: 24,
                ),
              ),
              Text(
                LocaleKeys.ball.tr(),
                style: context.textTheme.bodySmall!.copyWith(
                  color: package.is_selected ? AppColors.primary : AppColors.darkGrey,
                  fontSize: 12,
                ),
              ),
              const Gap(8),
              
              // Price
              Text(
                '${package.price_uzs.toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]} ')} ${LocaleKeys.som.tr()}',
                style: context.textTheme.bodySmall!.copyWith(
                  fontWeight: FontWeight.w600,
                  color: package.is_selected ? AppColors.primary : AppColors.black,
                  fontSize: 11,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

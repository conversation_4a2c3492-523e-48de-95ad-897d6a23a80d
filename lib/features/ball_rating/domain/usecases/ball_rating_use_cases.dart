import 'package:echipta/core/exceptions/failures.dart';
import 'package:echipta/core/singleton/singleton.dart';
import 'package:echipta/core/usecases/usecase.dart';
import 'package:echipta/core/utils/either.dart';
import 'package:echipta/features/ball_rating/data/repository/ball_rating_repository_impl.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_user_entity.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_history_entity.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_earning_match_entity.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_promotion_entity.dart';
import 'package:echipta/features/ball_rating/domain/entities/ball_package_entity.dart';
import 'package:echipta/features/ball_rating/domain/repository/ball_rating_repository.dart';

class GetBallRankingUseCase extends UseCase<List<BallUserEntity>, NoParams> {
  final BallRatingRepository _repository = serviceLocator<BallRatingRepositoryImpl>();
  
  @override
  Future<Either<Failure, List<BallUserEntity>>> call(NoParams params) async {
    return await _repository.getBallRanking();
  }
}

class GetBallHistoryUseCase extends UseCase<List<BallHistoryEntity>, NoParams> {
  final BallRatingRepository _repository = serviceLocator<BallRatingRepositoryImpl>();
  
  @override
  Future<Either<Failure, List<BallHistoryEntity>>> call(NoParams params) async {
    return await _repository.getBallHistory();
  }
}

class GetBallEarningMatchesUseCase extends UseCase<List<BallEarningMatchEntity>, NoParams> {
  final BallRatingRepository _repository = serviceLocator<BallRatingRepositoryImpl>();
  
  @override
  Future<Either<Failure, List<BallEarningMatchEntity>>> call(NoParams params) async {
    return await _repository.getBallEarningMatches();
  }
}

class GetBallPromotionsUseCase extends UseCase<List<BallPromotionEntity>, NoParams> {
  final BallRatingRepository _repository = serviceLocator<BallRatingRepositoryImpl>();
  
  @override
  Future<Either<Failure, List<BallPromotionEntity>>> call(NoParams params) async {
    return await _repository.getBallPromotions();
  }
}

class GetBallPackagesUseCase extends UseCase<List<BallPackageEntity>, NoParams> {
  final BallRatingRepository _repository = serviceLocator<BallRatingRepositoryImpl>();
  
  @override
  Future<Either<Failure, List<BallPackageEntity>>> call(NoParams params) async {
    return await _repository.getBallPackages();
  }
}

class PurchaseBallPackageUseCase extends UseCase<String?, int> {
  final BallRatingRepository _repository = serviceLocator<BallRatingRepositoryImpl>();
  
  @override
  Future<Either<Failure, String?>> call(int packageId) async {
    return await _repository.purchaseBallPackage(packageId);
  }
}

class GetCurrentUserBallInfoUseCase extends UseCase<BallUserEntity, NoParams> {
  final BallRatingRepository _repository = serviceLocator<BallRatingRepositoryImpl>();
  
  @override
  Future<Either<Failure, BallUserEntity>> call(NoParams params) async {
    return await _repository.getCurrentUserBallInfo();
  }
}

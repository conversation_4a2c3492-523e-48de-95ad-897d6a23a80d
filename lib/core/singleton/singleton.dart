import 'package:echipta/core/api/dio_settings.dart';
import 'package:echipta/core/storage/storage_repository.dart';
import 'package:echipta/core/storage/store_keys.dart';
import 'package:echipta/features/auth/data/datasource/auth_datasource.dart';
import 'package:echipta/features/auth/data/repository/auth_repository_impl.dart';
import 'package:echipta/features/ball_rating/data/datasource/ball_rating_datasource.dart';
import 'package:echipta/features/ball_rating/data/repository/ball_rating_repository_impl.dart';
import 'package:echipta/features/chat/data/datasource/chat_datasource.dart';
import 'package:echipta/features/chat/data/repository/chat_repository_impl.dart';
import 'package:echipta/features/gift/data/datasource/gift_datasource.dart';
import 'package:echipta/features/gift/data/repository/gift_repository_impl.dart';
import 'package:echipta/features/home/<USER>/datasource/home_datasource.dart';
import 'package:echipta/features/home/<USER>/repository/home_repository_impl.dart';
import 'package:echipta/features/order/data/datasource/order_datasource.dart';
import 'package:echipta/features/order/data/repository/order_repository_impl.dart';
import 'package:echipta/features/profile/data/datasource/profile_datasource.dart';
import 'package:echipta/features/profile/data/repository/profile_repository_impl.dart';
import 'package:echipta/features/rating/data/datasource/rank_datasource.dart';
import 'package:echipta/features/rating/data/repository/rank_repository_impl.dart';
import 'package:echipta/features/ticket/data/datasource/ticket_datasource.dart';
import 'package:echipta/features/ticket/data/repository/ticket_repository_impl.dart';
import 'package:echipta/core/services/notification_service.dart';
import 'package:get_it/get_it.dart';

final serviceLocator = GetIt.I;

Future<void> setupLocator({String? lang}) async {
  serviceLocator.registerLazySingleton(() => DioSettings()
    ..setBaseOptions(
        lang: StorageRepository.getString(StoreKeys.language, defValue: "uz")));

  serviceLocator.registerLazySingleton(() => AuthDatasourceImpl());
  serviceLocator.registerLazySingleton(() => AuthRepositoryImpl());

  serviceLocator.registerLazySingleton(() => ProfileDatasourceImpl());

  serviceLocator.registerLazySingleton(() => ProfileRepositoryImpl());

  serviceLocator.registerLazySingleton(() => HomeDatasourceImpl());

  serviceLocator.registerLazySingleton(() => HomeRepositoryImpl());

  serviceLocator.registerLazySingleton(() => RankDatasourceImpl());

  serviceLocator.registerLazySingleton(() => RankRepositoryImpl());

  serviceLocator.registerLazySingleton(() => ChatDatasourceImpl());

  serviceLocator.registerLazySingleton(() => ChatRepositoryImpl());

  serviceLocator.registerLazySingleton(() => TicketDatasourceImpl());

  serviceLocator.registerLazySingleton(() => TicketRepositoryImpl());

  serviceLocator.registerLazySingleton(() => OrderDatasourceImpl());

  serviceLocator.registerLazySingleton(() => OrderRepositoryImpl());

  serviceLocator.registerLazySingleton(() => GiftDatasourceImpl());

  serviceLocator.registerLazySingleton(() => GiftRepositoryImpl());

  serviceLocator.registerLazySingleton(() => BallRatingDataSourceImpl());

  serviceLocator.registerLazySingleton(() => BallRatingRepositoryImpl());

  // Register Notification Service
  serviceLocator.registerLazySingleton(() => NotificationService());
}

Future resetLocator({String? lang}) async {
  await serviceLocator.reset();
  await setupLocator(lang: lang);
}
